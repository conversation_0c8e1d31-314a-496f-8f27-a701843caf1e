{"log": {"level": "info"}, "dns": {"servers": [{"tag": "dns_proxy", "address": "*******"}, {"tag": "dns_direct", "address": "*********"}], "rules": [{"domain": ["geosite:cn"], "server": "dns_direct"}], "final": "dns_proxy", "strategy": "prefer_ipv4"}, "inbounds": [{"type": "socks", "tag": "socks_1099", "listen_port": 1099, "listen": "127.0.0.1"}, {"type": "socks", "tag": "socks_1098", "listen_port": 1098, "listen": "127.0.0.1"}, {"type": "socks", "tag": "socks_1111", "listen_port": 1111, "listen": "127.0.0.1"}, {"type": "socks", "tag": "socks_1112", "listen_port": 1112, "listen": "127.0.0.1"}], "outbounds": [{"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 45288, "tag": "socks_1099_815129be4c4e055c973ed280f4f4c4b8_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 39597, "tag": "socks_1099_1e58d7f1055bb8f23ec31da66139f564_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 28516, "tag": "socks_1099_99183558e15d255ffb4b6d92c698d2f4_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 45311, "tag": "socks_1099_5e5a288d83e1bd0bc28ac92ec6b25d13_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 28829, "tag": "socks_1099_30ed87faa1e039f2522b59894383a26f_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 45299, "tag": "socks_1099_212831b4f97c85bfcf702c75342b4abc_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 15305, "tag": "socks_1099_013cc90363e43193edc8b0b1818269e2_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 59793, "tag": "socks_1099_0318a0879d536cb7b605fd523c43e40d_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 46042, "tag": "socks_1099_6342dc0b12c8e3f893e42861e6791403_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 59969, "tag": "socks_1099_acf97a129cf17c588f285fca586a5680_proxy", "type": "shadowsocks"}, {"idle_timeout": "60s", "interval": "60s", "outbounds": ["socks_1099_815129be4c4e055c973ed280f4f4c4b8_proxy", "socks_1099_1e58d7f1055bb8f23ec31da66139f564_proxy", "socks_1099_99183558e15d255ffb4b6d92c698d2f4_proxy", "socks_1099_5e5a288d83e1bd0bc28ac92ec6b25d13_proxy", "socks_1099_30ed87faa1e039f2522b59894383a26f_proxy", "socks_1099_212831b4f97c85bfcf702c75342b4abc_proxy", "socks_1099_013cc90363e43193edc8b0b1818269e2_proxy", "socks_1099_0318a0879d536cb7b605fd523c43e40d_proxy", "socks_1099_6342dc0b12c8e3f893e42861e6791403_proxy", "socks_1099_acf97a129cf17c588f285fca586a5680_proxy"], "tag": "socks_1099_urltest", "type": "urltest", "url": "http://www.gstatic.com/generate_204"}, {"outbounds": ["socks_1099_urltest", "socks_1099_815129be4c4e055c973ed280f4f4c4b8_proxy", "socks_1099_1e58d7f1055bb8f23ec31da66139f564_proxy", "socks_1099_99183558e15d255ffb4b6d92c698d2f4_proxy", "socks_1099_5e5a288d83e1bd0bc28ac92ec6b25d13_proxy", "socks_1099_30ed87faa1e039f2522b59894383a26f_proxy", "socks_1099_212831b4f97c85bfcf702c75342b4abc_proxy", "socks_1099_013cc90363e43193edc8b0b1818269e2_proxy", "socks_1099_0318a0879d536cb7b605fd523c43e40d_proxy", "socks_1099_6342dc0b12c8e3f893e42861e6791403_proxy", "socks_1099_acf97a129cf17c588f285fca586a5680_proxy"], "tag": "socks_1099_selector", "type": "selector"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 48486, "tag": "socks_1098_e4f8e2108159770e4da5dd75543a3dd1_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 29120, "tag": "socks_1098_54dd251dbbb9e137ee29de0d8cebff3c_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 13171, "tag": "socks_1098_68f175d18713135cb94d2fd9a76373bd_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 58745, "tag": "socks_1098_7e363a675cef0aeb47e7cfe2fc9e8dd6_proxy", "type": "shadowsocks"}, {"idle_timeout": "60s", "interval": "60s", "outbounds": ["socks_1098_e4f8e2108159770e4da5dd75543a3dd1_proxy", "socks_1098_54dd251dbbb9e137ee29de0d8cebff3c_proxy", "socks_1098_68f175d18713135cb94d2fd9a76373bd_proxy", "socks_1098_7e363a675cef0aeb47e7cfe2fc9e8dd6_proxy"], "tag": "socks_1098_urltest", "type": "urltest", "url": "http://www.gstatic.com/generate_204"}, {"outbounds": ["socks_1098_urltest", "socks_1098_e4f8e2108159770e4da5dd75543a3dd1_proxy", "socks_1098_54dd251dbbb9e137ee29de0d8cebff3c_proxy", "socks_1098_68f175d18713135cb94d2fd9a76373bd_proxy", "socks_1098_7e363a675cef0aeb47e7cfe2fc9e8dd6_proxy"], "tag": "socks_1098_selector", "type": "selector"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 14765, "tag": "socks_1111_7f62538e9cda67ca49e5491e8e80b6d6_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 19957, "tag": "socks_1111_5f9f5ddb2590b2a8932bdfbf4b4944f1_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 27746, "tag": "socks_1111_eaf93a9ccfa655814fcdf1e969d3721c_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 41617, "tag": "socks_1111_2d13390b079a720546a14de471186783_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 39114, "tag": "socks_1111_462e928d22e40f127fa567bd3a7ac964_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 35998, "tag": "socks_1111_8a224daa94143f4b9aba34568cc3fbd6_proxy", "type": "shadowsocks"}, {"idle_timeout": "60s", "interval": "60s", "outbounds": ["socks_1111_7f62538e9cda67ca49e5491e8e80b6d6_proxy", "socks_1111_5f9f5ddb2590b2a8932bdfbf4b4944f1_proxy", "socks_1111_eaf93a9ccfa655814fcdf1e969d3721c_proxy", "socks_1111_2d13390b079a720546a14de471186783_proxy", "socks_1111_462e928d22e40f127fa567bd3a7ac964_proxy", "socks_1111_8a224daa94143f4b9aba34568cc3fbd6_proxy"], "tag": "socks_1111_urltest", "tolerance": 50, "type": "urltest", "url": "http://www.gstatic.com/generate_204"}, {"outbounds": ["socks_1111_urltest", "socks_1111_7f62538e9cda67ca49e5491e8e80b6d6_proxy", "socks_1111_5f9f5ddb2590b2a8932bdfbf4b4944f1_proxy", "socks_1111_eaf93a9ccfa655814fcdf1e969d3721c_proxy", "socks_1111_2d13390b079a720546a14de471186783_proxy", "socks_1111_462e928d22e40f127fa567bd3a7ac964_proxy", "socks_1111_8a224daa94143f4b9aba34568cc3fbd6_proxy"], "tag": "socks_1111_selector", "type": "selector"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "main-central.earthsurface.cc", "server_port": 35991, "tag": "socks_1112_431fa74e91fc4fd5ef4000cab8eaae9a_proxy", "type": "shadowsocks"}, {"method": "aes-128-gcm", "password": "f0377de9-deee-4f30-9980-ff06d59a950a", "server": "**************", "server_port": 10240, "tag": "socks_1112_7dfb5367831564d73b9ce1334f2121c3_proxy", "type": "shadowsocks"}, {"idle_timeout": "60s", "interval": "60s", "outbounds": ["socks_1112_431fa74e91fc4fd5ef4000cab8eaae9a_proxy", "socks_1112_7dfb5367831564d73b9ce1334f2121c3_proxy"], "tag": "socks_1112_urltest", "tolerance": 50, "type": "urltest", "url": "http://www.gstatic.com/generate_204"}, {"outbounds": ["socks_1112_urltest", "socks_1112_431fa74e91fc4fd5ef4000cab8eaae9a_proxy", "socks_1112_7dfb5367831564d73b9ce1334f2121c3_proxy"], "tag": "socks_1112_selector", "type": "selector"}, {"tag": "direct", "type": "direct"}], "route": {"rules": [{"inbound": ["socks_1099"], "outbound": "socks_1099_selector"}, {"inbound": ["socks_1098"], "outbound": "socks_1098_selector"}, {"inbound": ["socks_1111"], "outbound": "socks_1111_selector"}, {"inbound": ["socks_1112"], "outbound": "socks_1112_selector"}], "final": "direct", "auto_detect_interface": true}}